// Test complex object structures to ensure our fix handles edge cases
const { obfuscateCode } = require('./src/obfuscator');

const complexCode = `
const config = {
    "api_key": "secret123",
    database: {
        host: "localhost",
        port: 5432,
        "user": "admin",
        password: "password123"
    },
    features: {
        "logging": true,
        "debug": false,
        cache: "redis"
    }
};

const obj = {
    hello: "world",
    test: function() {
        return "test result";
    },
    nested: {
        value: "nested value",
        func: () => "arrow function"
    }
};

console.log(config.api_key);
console.log(obj.hello);
console.log(obj.nested.value);
`;

console.log('Testing complex object structures...');
console.log('Original code:');
console.log(complexCode);

async function testComplexObjects() {
    try {
        const result = await obfuscateCode(complexCode);
        console.log('\n✅ Complex object obfuscation successful!');
        console.log('Obfuscated code length:', result.length);
        
        // Test that the obfuscated code is valid JavaScript
        try {
            new Function(result);
            console.log('✅ Generated code is syntactically valid');
        } catch (syntaxError) {
            console.error('❌ Generated code has syntax errors:', syntaxError.message);
        }
    } catch (error) {
        console.error('\n❌ Error in complex object obfuscation:');
        console.error(error.message);
        console.error(error.stack);
    }
}

testComplexObjects();
