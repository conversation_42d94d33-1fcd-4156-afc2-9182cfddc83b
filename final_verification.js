// Final verification test
const { processStringEncoding } = require('./src/string.js');

// Test with a simple, known string
const testCode = `console.log("Hello World!");`;

console.log('='.repeat(60));
console.log('FINAL VERIFICATION TEST');
console.log('='.repeat(60));

console.log('\nOriginal code:');
console.log(testCode);

const result = processStringEncoding(testCode);

console.log('\nGenerated code:');
console.log(result.code);

console.log('\nString array contents:');
result.base64StringArray.forEach((str, index) => {
    console.log(`[${index}]: ${str}`);
});

// Extract the index being used for "Hello World!"
const codeLines = result.code.split('\n');
const consoleLogLine = codeLines.find(line => line.includes('console.log(customDecode'));
console.log('\nGenerated console.log line:');
console.log(consoleLogLine);

const match = consoleLogLine.match(/base64Strings\[(\d+)\]/);
if (match) {
    const usedIndex = parseInt(match[1]);
    console.log(`\nCode is using index ${usedIndex}`);
    console.log(`String at that index: "${result.base64StringArray[usedIndex]}"`);
    
    // Test manual decoding
    const encodedStr = result.base64StringArray[usedIndex];
    console.log(`\nManual decoding of "${encodedStr}":`);
    
    // Step 1: Remove noise
    let cleanStr = '';
    for (let i = 0; i < encodedStr.length; i++) {
        if ((i + 1) % 3 !== 0) {
            cleanStr += encodedStr[i];
        }
    }
    console.log(`After noise removal: "${cleanStr}"`);
    
    // Step 2: Restore padding
    while (cleanStr.length % 4 !== 0) {
        cleanStr += '=';
    }
    console.log(`With padding restored: "${cleanStr}"`);
    
    // Step 3: Decode using Node.js built-in
    try {
        const nodeDecoded = Buffer.from(cleanStr, 'base64').toString('utf8');
        console.log(`Node.js decoded: "${nodeDecoded}"`);
        console.log(`Matches expected: ${nodeDecoded === "Hello World!"}`);
    } catch (error) {
        console.log(`Node.js decode error: ${error.message}`);
    }
    
    // Step 4: Test with our custom decoder
    function customDecode(encodedStr) {
        let cleanStr = "";
        for (let i = 0; i < encodedStr.length; i++) {
            if ((i + 1) % 3 !== 0) cleanStr += encodedStr[i];
        }
        while (cleanStr.length % 4 !== 0) cleanStr += "=";
        const base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        let result = "",
            buffer = 0,
            bitsCollected = 0;
        for (let i = 0; i < cleanStr.length; i++) {
            const char = cleanStr[i];
            const charIndex = base64Chars.indexOf(char);
            if (charIndex === -1) continue;
            buffer = buffer << 6 | charIndex;
            bitsCollected += 6;
            if (bitsCollected >= 8) {
                bitsCollected -= 8;
                result += String.fromCharCode(buffer >> bitsCollected & 255);
            }
        }
        return result;
    }
    
    const customDecoded = customDecode(encodedStr);
    console.log(`Custom decoded: "${customDecoded}"`);
    console.log(`Custom matches expected: ${customDecoded === "Hello World!"}`);
}

// Test execution
console.log('\n' + '='.repeat(60));
console.log('EXECUTION TEST:');
console.log('='.repeat(60));

const fs = require('fs');
fs.writeFileSync('temp_final_test.js', result.code);

try {
    const { execSync } = require('child_process');
    const output = execSync('node temp_final_test.js', { encoding: 'utf8' });
    console.log('✓ Execution successful!');
    console.log('Output:', JSON.stringify(output));
    
    if (output.trim() === 'Hello World!') {
        console.log('🎉 PERFECT! The obfuscated code produces the correct output!');
    } else {
        console.log('⚠️  Output does not match expected "Hello World!"');
    }
    
} catch (error) {
    console.log('✗ Execution failed:');
    console.log(error.message);
} finally {
    fs.unlinkSync('temp_final_test.js');
}
