// Test to specifically verify that object property keys are being encoded
const { processStringEncoding } = require('./src/string.js');

const testCode = `
const obj = {
    "hello": "world",
    "test": "value",
    "api_key": "secret123"
};

const config = {
    database: "localhost",
    "user": "admin",
    password: "password123"
};

console.log(obj.hello);
console.log(config.user);
`;

console.log('Testing Object Property Key Encoding');
console.log('====================================');
console.log('\nOriginal code:');
console.log(testCode);

const result = processStringEncoding(testCode);

console.log('\n====================================');
console.log('OBFUSCATED CODE:');
console.log('====================================');
console.log(result.code);

console.log('\n====================================');
console.log('ANALYSIS:');
console.log('====================================');
console.log(`✓ Transformed strings: ${result.transformCount}`);
console.log(`✓ Total strings in array: ${result.base64StringArray.length}`);

// Check if the obfuscated code contains computed properties
const hasComputedProperties = result.code.includes('[customDecode(');
console.log(`✓ Contains computed properties with encoded keys: ${hasComputedProperties}`);

// Check if original property names are still visible
const originalKeys = ['hello', 'test', 'api_key', 'database', 'user', 'password'];
let hiddenKeys = 0;
originalKeys.forEach(key => {
    const isHidden = !result.code.includes(`"${key}"`) && !result.code.includes(`'${key}'`) && !result.code.includes(key + ':');
    if (isHidden) {
        hiddenKeys++;
        console.log(`✓ Property key "${key}" is encoded/hidden`);
    } else {
        console.log(`⚠ Property key "${key}" is still visible`);
    }
});

console.log(`\n✓ Hidden property keys: ${hiddenKeys}/${originalKeys.length}`);

console.log('\n====================================');
console.log('STRING ARRAY CONTENTS:');
console.log('====================================');
result.base64StringArray.forEach((str, index) => {
    console.log(`[${index.toString().padStart(2, ' ')}]: ${str}`);
});

// Test execution
console.log('\n====================================');
console.log('EXECUTION TEST:');
console.log('====================================');
try {
    // Create a function from the obfuscated code and test it
    const testFunc = new Function(result.code);
    console.log('✓ Obfuscated code is syntactically valid');
    
    // Try to execute it (capture console.log output)
    const originalLog = console.log;
    const outputs = [];
    console.log = (...args) => outputs.push(args.join(' '));
    
    try {
        testFunc();
        console.log = originalLog;
        console.log('✓ Obfuscated code executed successfully');
        console.log('✓ Execution outputs:', outputs);
    } catch (execError) {
        console.log = originalLog;
        console.log('⚠ Execution error:', execError.message);
    }
} catch (syntaxError) {
    console.log('❌ Syntax error:', syntaxError.message);
}
