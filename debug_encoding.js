// Debug the encoding/decoding process

function generateNoise(count) {
    const noiseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let noise = '';
    for (let i = 0; i < count; i++) {
        noise += noiseChars[Math.floor(Math.random() * noiseChars.length)];
    }
    return noise;
}

function enhancedEncodeToBase64(str) {
    // Standard base64 encoding
    let base64 = Buffer.from(str, 'utf8').toString('base64');
    console.log(`Original string: "${str}"`);
    console.log(`Standard base64: "${base64}"`);
    
    // Remove padding
    base64 = base64.replace(/=/g, '');
    console.log(`Without padding: "${base64}"`);
    
    // Add noise characters at regular intervals
    let noisyStr = '';
    for (let i = 0; i < base64.length; i++) {
        noisyStr += base64[i];
        // Add noise every 2 characters
        if ((i + 1) % 2 === 0) {
            noisyStr += generateNoise(1);
        }
    }
    console.log(`With noise: "${noisyStr}"`);
    
    return noisyStr;
}

function customDecode(encodedStr) {
    console.log(`\nDecoding: "${encodedStr}"`);
    
    // Remove noise characters that were added every 2 characters
    let cleanStr = '';
    for (let i = 0; i < encodedStr.length; i++) {
        // Skip every 3rd character (noise characters at positions 2, 5, 8, etc.)
        if ((i + 1) % 3 !== 0) {
            cleanStr += encodedStr[i];
        }
    }
    console.log(`After noise removal: "${cleanStr}"`);
    
    // Restore padding
    while (cleanStr.length % 4 !== 0) {
        cleanStr += '=';
    }
    console.log(`With padding restored: "${cleanStr}"`);
    
    // Custom base64 decode logic
    const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let buffer = 0;
    let bitsCollected = 0;
    
    for (let i = 0; i < cleanStr.length; i++) {
        const char = cleanStr[i];
        const charIndex = base64Chars.indexOf(char);
        
        if (charIndex === -1) continue;
        
        buffer = (buffer << 6) | charIndex;
        bitsCollected += 6;
        
        if (bitsCollected >= 8) {
            bitsCollected -= 8;
            result += String.fromCharCode((buffer >> bitsCollected) & 0xFF);
        }
    }
    
    console.log(`Final result: "${result}"`);
    return result;
}

// Test with a simple string
const testString = "Hello World!";
const encoded = enhancedEncodeToBase64(testString);
const decoded = customDecode(encoded);

console.log(`\n${'='.repeat(50)}`);
console.log(`Original: "${testString}"`);
console.log(`Encoded: "${encoded}"`);
console.log(`Decoded: "${decoded}"`);
console.log(`Match: ${testString === decoded}`);

// Test with Node.js built-in for comparison
console.log(`\n${'='.repeat(50)}`);
console.log('Comparison with Node.js built-in:');
const nodeEncoded = Buffer.from(testString, 'utf8').toString('base64');
const nodeDecoded = Buffer.from(nodeEncoded, 'base64').toString('utf8');
console.log(`Node encoded: "${nodeEncoded}"`);
console.log(`Node decoded: "${nodeDecoded}"`);
console.log(`Node match: ${testString === nodeDecoded}`);
