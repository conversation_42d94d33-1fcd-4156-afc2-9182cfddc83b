const { processStringEncoding } = require('./src/string.js');

// Test code with various string literals
const testCode = `
console.log("Hello World!");
const message = "This is a test message";
const greeting = \`Welcome to our application\`;
const data = "Some important data";
const config = "configuration settings";
`;

console.log('='.repeat(60));
console.log('ENHANCED STRING OBFUSCATION TEST');
console.log('='.repeat(60));

console.log('\nOriginal code:');
console.log(testCode);

// Process the code
const result = processStringEncoding(testCode);

console.log('\n' + '='.repeat(60));
console.log('OBFUSCATED CODE:');
console.log('='.repeat(60));
console.log(result.code);

console.log('\n' + '='.repeat(60));
console.log('STATISTICS:');
console.log('='.repeat(60));
console.log(`✓ Transformed strings: ${result.transformCount}`);
console.log(`✓ Total strings in array: ${result.base64StringArray.length}`);
console.log(`✓ Decoy strings included: ${result.base64StringArray.length - result.transformCount}`);

console.log('\n' + '='.repeat(60));
console.log('FEATURES IMPLEMENTED:');
console.log('='.repeat(60));
console.log('✓ Custom decoder function (no atob dependency)');
console.log('✓ Enhanced encoding with noise injection');
console.log('✓ Padding removal from encoded strings');
console.log('✓ Decoy strings mixed in the array');
console.log('✓ Noise removal process in decoder');
console.log('✓ Random placement of decoy strings');

console.log('\n' + '='.repeat(60));
console.log('STRING ARRAY ANALYSIS:');
console.log('='.repeat(60));
result.base64StringArray.forEach((str, index) => {
    const isDecoy = index < result.base64StringArray.length - result.transformCount || 
                   !result.code.includes(`base64Strings[${index}]`);
    console.log(`[${index.toString().padStart(2)}]: ${str.substring(0, 30)}${str.length > 30 ? '...' : ''} ${isDecoy ? '(DECOY)' : '(REAL)'}`);
});

// Test execution of the obfuscated code
console.log('\n' + '='.repeat(60));
console.log('TESTING OBFUSCATED CODE EXECUTION:');
console.log('='.repeat(60));

try {
    // Write the obfuscated code to a temporary file and execute it
    const fs = require('fs');
    fs.writeFileSync('temp_obfuscated.js', result.code);
    
    const { execSync } = require('child_process');
    const output = execSync('node temp_obfuscated.js', { encoding: 'utf8' });
    
    console.log('✓ Obfuscated code executed successfully!');
    console.log('Output:');
    console.log(output);
    
    // Clean up
    fs.unlinkSync('temp_obfuscated.js');
    
} catch (error) {
    console.log('✗ Error executing obfuscated code:');
    console.log(error.message);
}

console.log('\n' + '='.repeat(60));
console.log('SECURITY FEATURES:');
console.log('='.repeat(60));
console.log('✓ No dependency on browser atob() function');
console.log('✓ Custom base64 implementation harder to detect');
console.log('✓ Noise injection makes strings look random');
console.log('✓ Decoy strings confuse static analysis');
console.log('✓ Padding removal reduces recognizable patterns');
console.log('✓ Random noise placement increases entropy');
