const { processStringEncoding } = require('./src/string.js');

// Test code with various string literals
const testCode = `
console.log("Hello World!");
const message = "This is a test message";
const greeting = \`Welcome to our application\`;
const data = "Some important data";
const config = "configuration settings";
`;

console.log('Original code:');
console.log(testCode);
console.log('\n' + '='.repeat(50) + '\n');

// Process the code
const result = processStringEncoding(testCode);

console.log('Obfuscated code:');
console.log(result.code);
console.log('\n' + '='.repeat(50) + '\n');

console.log('Statistics:');
console.log(`Transformed strings: ${result.transformCount}`);
console.log(`Total strings in array: ${result.base64StringArray.length}`);
console.log('\nString array contents:');
result.base64StringArray.forEach((str, index) => {
    console.log(`[${index}]: ${str}`);
});

// Test the enhanced encoding function directly
console.log('\n' + '='.repeat(50) + '\n');
console.log('Testing enhanced encoding:');

// We need to access the internal functions for testing
const fs = require('fs');
const fileContent = fs.readFileSync('./src/string.js', 'utf8');

// Extract and test the enhanced encoding
const testString = "Hello World!";
console.log(`Original: "${testString}"`);

// Simulate the enhanced encoding process
const Buffer = require('buffer').Buffer;
let base64 = Buffer.from(testString, 'utf8').toString('base64');
console.log(`Standard Base64: "${base64}"`);

// Remove padding
base64 = base64.replace(/=/g, '');
console.log(`Without padding: "${base64}"`);

// Add noise (simplified version)
let noisyStr = '';
const noiseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
for (let i = 0; i < base64.length; i++) {
    noisyStr += base64[i];
    if ((i + 1) % 2 === 0) {
        noisyStr += noiseChars[Math.floor(Math.random() * noiseChars.length)];
    }
}
console.log(`With noise: "${noisyStr}"`);
