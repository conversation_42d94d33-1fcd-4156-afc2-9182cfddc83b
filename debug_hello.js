// Debug why "Hello" is not being encoded
const { processStringEncoding } = require('./src/string.js');

console.log('Testing "Hello" encoding:');

// Test the enhanced encoding function directly
function enhancedEncodeToBase64(str) {
    try {
        if (typeof str !== 'string') {
            console.warn('enhancedEncodeToBase64: Input is not a string:', typeof str, str);
            return '';
        }
        if (str.length > 100000) {
            console.warn('enhancedEncodeToBase64: String too long, skipping:', str.length);
            return str;
        }
        
        console.log(`Encoding: "${str}" (length: ${str.length})`);
        
        // Standard base64 encoding
        let base64 = Buffer.from(str, 'utf8').toString('base64');
        console.log(`Base64: "${base64}"`);
        
        // Remove padding
        base64 = base64.replace(/=/g, '');
        console.log(`No padding: "${base64}"`);
        
        // Add noise characters at regular intervals
        let noisyStr = '';
        for (let i = 0; i < base64.length; i++) {
            noisyStr += base64[i];
            // Add noise every 2 characters
            if ((i + 1) % 2 === 0) {
                const noiseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                noisyStr += noiseChars[Math.floor(Math.random() * noiseChars.length)];
            }
        }
        console.log(`With noise: "${noisyStr}"`);
        
        return noisyStr;
    } catch (error) {
        console.error('Error encoding string to Base64:', error, 'String:', str);
        return str;
    }
}

// Test with "Hello"
const result1 = enhancedEncodeToBase64("Hello");
console.log(`Final result: "${result1}"`);
console.log(`Same as input: ${result1 === "Hello"}`);

console.log('\n' + '='.repeat(50));

// Test with longer string
const result2 = enhancedEncodeToBase64("Hello World");
console.log(`Final result: "${result2}"`);
console.log(`Same as input: ${result2 === "Hello World"}`);

console.log('\n' + '='.repeat(50));

// Test the actual processing
const simpleCode = `console.log("Hello World");`;
console.log('Processing code:', simpleCode);

const result = processStringEncoding(simpleCode);
console.log('\nGenerated code:');
console.log(result.code);

console.log('\nStatistics:');
console.log('Transform count:', result.transformCount);
console.log('Array length:', result.base64StringArray.length);
