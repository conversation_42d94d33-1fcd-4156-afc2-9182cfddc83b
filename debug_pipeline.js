// Debug the full obfuscation pipeline to reproduce the error
const { obfuscateCode } = require('./src/obfuscator');

// Test with simple code that might trigger the issue
const testCode = `
const obj = {
    hello: "world",
    test: function() {
        return "test";
    }
};
console.log(obj.hello);
`;

console.log('Testing full obfuscation pipeline...');
console.log('Original code:');
console.log(testCode);

async function testPipeline() {
    try {
        const result = await obfuscateCode(testCode);
        console.log('\nObfuscated successfully!');
        console.log(result);
    } catch (error) {
        console.error('\nError in pipeline:');
        console.error(error.message);
        console.error(error.stack);
    }
}

testPipeline();
