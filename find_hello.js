// Find which string in the array should decode to "Hello"
const { processStringEncoding } = require('./src/string.js');

const simpleCode = `console.log("Hello");`;
const result = processStringEncoding(simpleCode);

console.log('Generated code:');
console.log(result.code);

console.log('\nTesting each string in the array:');

// Custom decode function (copied from generated code)
function customDecode(encodedStr) {
  let cleanStr = "";
  for (let i = 0; i < encodedStr.length; i++) {
    if ((i + 1) % 3 !== 0) cleanStr += encodedStr[i];
  }
  while (cleanStr.length % 4 !== 0) cleanStr += "=";
  const base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
  let result = "",
    buffer = 0,
    bitsCollected = 0;
  for (let i = 0; i < cleanStr.length; i++) {
    const char = cleanStr[i];
    const charIndex = base64Chars.indexOf(char);
    if (charIndex === -1) continue;
    buffer = buffer << 6 | charIndex;
    bitsCollected += 6;
    if (bitsCollected >= 8) {
      bitsCollected -= 8;
      result += String.fromCharCode(buffer >> bitsCollected & 255);
    }
  }
  return result;
}

result.base64StringArray.forEach((str, index) => {
    try {
        const decoded = customDecode(str);
        console.log(`[${index}]: "${str}" -> "${decoded}"`);
        if (decoded === "Hello") {
            console.log(`  *** FOUND "Hello" at index ${index} ***`);
        }
    } catch (error) {
        console.log(`[${index}]: "${str}" -> ERROR: ${error.message}`);
    }
});

// Check what the generated code is actually trying to decode
const codeLines = result.code.split('\n');
const consoleLogLine = codeLines.find(line => line.includes('console.log(customDecode'));
console.log('\nGenerated console.log line:');
console.log(consoleLogLine);

// Extract the index being used
const match = consoleLogLine.match(/base64Strings\[(\d+)\]/);
if (match) {
    const usedIndex = parseInt(match[1]);
    console.log(`\nCode is using index ${usedIndex}`);
    console.log(`String at that index: "${result.base64StringArray[usedIndex]}"`);
    console.log(`Decodes to: "${customDecode(result.base64StringArray[usedIndex])}"`);
}
