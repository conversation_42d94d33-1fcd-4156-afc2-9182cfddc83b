// This is the obfuscated code generated by our new system
const base64Strings = ["ZGcFi0wYXyFu3MNWwY4pbA2", "dGuVHzOdHoBlCb0zoNlpaws", "c2UFUtKcG1xlBZxmkFyEaWC03zY", "dmoFKsPdW4V1yYGnBlmNOGgoJ", "c2WFxtbcGKxldaimjx5vdGSE43f", "dGzVCzPdHep0FcFm7Y0eMwX", "c3WRey4aW95nuMNTkBoyOXgcf5I", "aGgVJsZbG88xCau2j0zwZGR8Y", "SGfVWsCbGV8gjV22a9yQbGmQzhp", "dGyV4zldHpZnXZKGOZkvbwl", "VG1hXpjcyUBpPcWycBh7IHORMlUc3fQg7bNWcVzJc2SFunEZQ1", "d2Q9Dy6bGVRylZQmTZzmM2AYW", "V2YV5sAY2j9tXZ1SeB0LbygBWvYdXyIgrYNXxBwJbG8lPjrYX2Rp1bw2v4", "U2y95tLZSSBpWbMX8BvVcnqROh0bnJQgiZIGbF05YQX", "Y2m9XuMZmElnJdNXeJhrdGUlkvIbilBz3ZaX5R0laWE56nVcwJ"];

function customDecode(encodedStr) {
  let cleanStr = "";
  for (let i = 0; i < encodedStr.length; i++) {
    if ((i + 1) % 3 !== 0) cleanStr += encodedStr[i];
  }
  while (cleanStr.length % 4 !== 0) cleanStr += "=";
  const base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
  let result = "",
    buffer = 0,
    bitsCollected = 0;
  for (let i = 0; i < cleanStr.length; i++) {
    const char = cleanStr[i];
    const charIndex = base64Chars.indexOf(char);
    if (charIndex === -1) continue;
    buffer = buffer << 6 | charIndex;
    bitsCollected += 6;
    if (bitsCollected >= 8) {
      bitsCollected -= 8;
      result += String.fromCharCode(buffer >> bitsCollected & 255);
    }
  }
  return result;
}

console.log(customDecode(base64Strings[8]));
const message = customDecode(base64Strings[9]);
const greeting = customDecode(base64Strings[10]);
const data = customDecode(base64Strings[11]);
const config = customDecode(base64Strings[12]);

console.log("Message:", message);
console.log("Greeting:", greeting);
console.log("Data:", data);
console.log("Config:", config);
